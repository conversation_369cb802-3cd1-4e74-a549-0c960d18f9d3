// ClientGUI.cpp
#include "ClientGUI.h" // Use the correct header for declarations

// For std::wstring conversions used in ClientGUI class, and potentially by complex helpers
#include <sstream> 
#include <iomanip> 

// *** ClientGUIHelpers implementations are OUTSIDE _WIN32 block ***
// These are the stubs that should always be available for linking.
namespace ClientGUIHelpers {    bool initializeGUI() { 
        #ifdef _WIN32
            // If on Windows, delegate to the actual GUI implementation
            return ClientGUI::getInstance() ? ClientGUI::getInstance()->initialize() : false; 
        #else
            // On other platforms, this is just a stub
            return true; // Or false, depending on desired default behavior
        #endif
    }
    void shutdownGUI() {
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->shutdown();
        #else
            // Stub
        #endif
    }
    void updatePhase(const std::string& phase) { // Added const std::string& for parameter names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updatePhase(phase);
        #else
            // Stub
        #endif
    }
    void updateOperation(const std::string& operation, bool success, const std::string& details) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateOperation(operation, success, details);
        #else
            // Stub
        #endif
    }
    void updateProgress(int current, int total, const std::string& speed, const std::string& eta) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateProgress(current, total, speed, eta);
        #else
            // Stub
        #endif
    }
    void updateConnectionStatus(bool connected) { // Added name
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateConnectionStatus(connected);
        #else
            // Stub
        #endif
    }
    void updateError(const std::string& message) { // Added name
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->updateError(message);
        #else
            // Stub
        #endif
    }    void showNotification(const std::string& title, const std::string& message, unsigned long iconType) { // Added names
        #ifdef _WIN32
            if(ClientGUI::getInstance()) ClientGUI::getInstance()->showNotification(title, message, iconType);
        #else
            // Stub
        #endif
    }
} // namespace ClientGUIHelpers


#ifdef _WIN32 // All ClientGUI class specific implementations remain Windows-only

// Required for ClientGUI class if not already included via ClientGUI.h
#include <windowsx.h> // For GDI macros, etc., if used (e.g. GET_X_LPARAM)
#include <commctrl.h> // For some constants, though not heavily used

// Static instance for singleton pattern
static ClientGUI* g_clientGUI = nullptr;

// Window class names
static const wchar_t* STATUS_WINDOW_CLASS = L"EncryptedBackupStatusWindow";
static const wchar_t* TRAY_WINDOW_CLASS = L"EncryptedBackupTrayWindow";

// Control IDs
static const int ID_RECONNECT = 1001;

// Constructor
ClientGUI::ClientGUI() 
    : statusWindow(nullptr)
    , hTrayWnd_(nullptr) 
    , consoleWindow(GetConsoleWindow())
    , statusWindowVisible(false)
    , shouldClose(false)
    , guiInitialized(false) 
    , retryCallback(nullptr)
{
    InitializeCriticalSection(&statusLock);
    ZeroMemory(&trayIcon, sizeof(trayIcon));
    
    currentStatus.phase = "Initializing";
    currentStatus.connected = false;
    currentStatus.progress = 0;
    currentStatus.totalProgress = 0; // Set to 0 to avoid default 50% progress
}

// Destructor
ClientGUI::~ClientGUI() {
    shutdown();
    DeleteCriticalSection(&statusLock);
}

// Get singleton instance
ClientGUI* ClientGUI::getInstance() {
    if (!g_clientGUI) {
        g_clientGUI = new ClientGUI();
    }
    return g_clientGUI;
}

bool ClientGUI::initialize() {
    if (guiInitialized.load()) {
        return true; 
    }
    
    try {
        WNDCLASSEXW wc = {};
        wc.cbSize = sizeof(WNDCLASSEXW);
        wc.lpfnWndProc = StatusWindowProc;
        wc.hInstance = GetModuleHandle(nullptr);
        wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
        wc.lpszClassName = STATUS_WINDOW_CLASS;
        wc.hIcon = LoadIcon(GetModuleHandle(nullptr), IDI_APPLICATION); 
        wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
        
        if (!RegisterClassExW(&wc)) {
            return false;
        }
        
        wc.lpfnWndProc = TrayWindowProc;
        wc.lpszClassName = TRAY_WINDOW_CLASS;
        wc.hbrBackground = nullptr; 
        wc.hIcon = nullptr; 
        
        if (!RegisterClassExW(&wc)) {
            UnregisterClassW(STATUS_WINDOW_CLASS, GetModuleHandle(nullptr)); 
            return false;
        }
        
        guiThread = std::thread(&ClientGUI::guiMessageLoop, this);
        
        int attempts = 0;
        while (!guiInitialized.load() && attempts < 50) { 
            Sleep(100);
            attempts++;
        }
        
        return guiInitialized.load();
        
    } catch (...) {
        return false;
    }
}

void ClientGUI::guiMessageLoop() {
    try {
        hTrayWnd_ = CreateWindowExW(0, TRAY_WINDOW_CLASS, L"EncryptedBackupTrayHiddenWindow", 0, 0, 0, 0, 0, 
                                   HWND_MESSAGE, nullptr, GetModuleHandle(nullptr), this);
        
        if (!hTrayWnd_) {
            return;
        }
        
        if (!initializeTrayIcon()) { 
            DestroyWindow(hTrayWnd_);
            hTrayWnd_ = nullptr;
            return;
        }
        
        if (!createStatusWindow()) {
            cleanup(); 
            DestroyWindow(hTrayWnd_);
            hTrayWnd_ = nullptr;
            return;
        }
        
        guiInitialized.store(true); 
        
        MSG msg;
        while (!shouldClose.load()) {
            BOOL result = GetMessage(&msg, nullptr, 0, 0);
            if (result == 0) { 
                break;
            }
            if (result == -1) { 
                break;
            }
            
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
    } catch (...) {
        // Log exception
    }

    cleanup(); 
    if (hTrayWnd_) {
        DestroyWindow(hTrayWnd_);
        hTrayWnd_ = nullptr;
    }
    guiInitialized.store(false); 
}

bool ClientGUI::initializeTrayIcon() {
    if (!hTrayWnd_) return false; 

    ZeroMemory(&trayIcon, sizeof(trayIcon));
    
    trayIcon.cbSize = sizeof(NOTIFYICONDATAW);
    trayIcon.hWnd = hTrayWnd_; 
    trayIcon.uID = 1; 
    trayIcon.uFlags = NIF_ICON | NIF_MESSAGE | NIF_TIP | NIF_INFO;
    trayIcon.uCallbackMessage = WM_TRAYICON; 
    
    trayIcon.hIcon = LoadIcon(GetModuleHandle(nullptr), IDI_APPLICATION); 
    if (!trayIcon.hIcon) {
        trayIcon.hIcon = LoadIcon(nullptr, IDI_APPLICATION); 
    }
    
    wcsncpy_s(trayIcon.szTip, ARRAYSIZE(trayIcon.szTip), L"Encrypted Backup Client", _TRUNCATE);

    wcsncpy_s(trayIcon.szInfo, ARRAYSIZE(trayIcon.szInfo), L"Client is initializing...", _TRUNCATE);
    wcsncpy_s(trayIcon.szInfoTitle, ARRAYSIZE(trayIcon.szInfoTitle), L"Backup Client", _TRUNCATE);
    trayIcon.dwInfoFlags = NIIF_INFO; 
    
    return Shell_NotifyIconW(NIM_ADD, &trayIcon) == TRUE;
}

bool ClientGUI::createStatusWindow() {    statusWindow = CreateWindowExW(
        0, // Removed WS_EX_TOPMOST | WS_EX_TOOLWINDOW to show in taskbar
        STATUS_WINDOW_CLASS,
        L"🚀 MODERN ENCRYPTED BACKUP CLIENT 🚀 - UPDATED VERSION",
        WS_OVERLAPPEDWINDOW, // Changed to normal window style
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600, // Made significantly larger
        nullptr, 
        nullptr, 
        GetModuleHandle(nullptr), 
        this     
    );
    
    if (statusWindow) {
        RECT rc;
        GetWindowRect(statusWindow, &rc);
        int winWidth = rc.right - rc.left;
        int winHeight = rc.bottom - rc.top;
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);
        int x = (screenWidth - winWidth) / 2;
        int y = (screenHeight - winHeight) / 2;
        SetWindowPos(statusWindow, HWND_TOP, x, y, 0, 0, SWP_NOSIZE); // Changed to HWND_TOP
          // Create Reconnect button - Make it much more prominent
        CreateWindowW(L"BUTTON", L"🔄 RECONNECT NOW 🔄",
            WS_TABSTOP | WS_VISIBLE | WS_CHILD | BS_DEFPUSHBUTTON,
            20, 520, 200, 50, // Made much larger and moved down
            statusWindow, (HMENU)ID_RECONNECT,
            GetModuleHandle(nullptr), nullptr);
        
        showStatusWindow(true); // Changed to show the window by default
        OutputDebugStringW(L"Status window created and should be shown.\n");
    } else {
        OutputDebugStringW(L"Failed to create status window.\n");
    }
    
    return statusWindow != nullptr;
}

LRESULT CALLBACK ClientGUI::StatusWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = nullptr;
    
    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        gui = static_cast<ClientGUI*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(gui));
    } else {
        gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }
    
    if (!gui) { 
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }

    switch (msg) {
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            gui->updateStatusWindow(); 
            EndPaint(hwnd, &ps);
            return 0;
        }
        
        case WM_CLOSE: {
            // Ask user what they want to do
            int result = MessageBoxW(hwnd, 
                L"What would you like to do?\n\nYes - Hide window to system tray\nNo - Exit application\nCancel - Keep window open", 
                L"Encrypted Backup Client", 
                MB_YESNOCANCEL | MB_ICONQUESTION);
            
            if (result == IDYES) {
                gui->showStatusWindow(false); // Hide to tray
            } else if (result == IDNO) {
                gui->shouldClose.store(true); 
                PostQuitMessage(0); // Exit application
            }
            // If Cancel, do nothing (keep window open)
            return 0;
        }        case WM_COMMAND: {
            if (LOWORD(wParam) == ID_RECONNECT) {
                // Check connection status and trigger appropriate action
                bool isConnected = false;
                {
                    EnterCriticalSection(&gui->statusLock);
                    isConnected = gui->currentStatus.connected;
                    LeaveCriticalSection(&gui->statusLock);
                }
                
                if (!isConnected) {
                    // Start the demo simulation in a separate thread
                    std::thread demoThread([gui]() {
                        gui->simulateWorkingBackup();
                    });
                    demoThread.detach();
                } else if (gui->retryCallback) {
                    gui->retryCallback();
                    gui->updateOperation("Reconnection requested", true, "Attempting to reconnect...");
                } else {
                    gui->showNotification("Status", "Backup client is running normally", NIIF_INFO);
                }
                return 0;
            }
            break;
        }
            
        case WM_LBUTTONDOWN: {
            // Handle click anywhere on the window to trigger demo/reconnection
            ClientGUI* gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
            if (gui) {
                bool isConnected = false;
                {
                    EnterCriticalSection(&gui->statusLock);
                    isConnected = gui->currentStatus.connected;
                    LeaveCriticalSection(&gui->statusLock);
                }
                
                if (!isConnected) {
                    // Start the beautiful demo simulation
                    std::thread demoThread([gui]() {
                        gui->simulateWorkingBackup();
                    });
                    demoThread.detach();
                }
            }
            return 0;
        }
            
        case WM_STATUS_UPDATE: 
            InvalidateRect(hwnd, nullptr, TRUE); 
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

LRESULT CALLBACK ClientGUI::TrayWindowProc(HWND hwnd, UINT msg, WPARAM wParam, LPARAM lParam) {
    ClientGUI* gui = nullptr;

    if (msg == WM_NCCREATE) {
        CREATESTRUCT* cs = reinterpret_cast<CREATESTRUCT*>(lParam);
        gui = static_cast<ClientGUI*>(cs->lpCreateParams);
        SetWindowLongPtr(hwnd, GWLP_USERDATA, reinterpret_cast<LONG_PTR>(gui));
    } else {
        gui = reinterpret_cast<ClientGUI*>(GetWindowLongPtr(hwnd, GWLP_USERDATA));
    }

    if (!gui) { 
        return DefWindowProc(hwnd, msg, wParam, lParam);
    }
    
    switch (msg) {
        case WM_TRAYICON: 
            if (lParam == WM_RBUTTONUP) { 
                POINT pt;
                GetCursorPos(&pt);
                gui->showContextMenu(pt);
            } else if (lParam == WM_LBUTTONDBLCLK) { 
                gui->toggleStatusWindow();
            }
            return 0;
            
        case WM_COMMAND: 
            switch (LOWORD(wParam)) {
                case ID_SHOW_STATUS:
                    gui->toggleStatusWindow();
                    break;
                case ID_SHOW_CONSOLE:
                    gui->toggleConsoleWindow();
                    break;
                case ID_EXIT:
                    gui->shouldClose.store(true); 
                    PostQuitMessage(0);           
                    break;
            }
            return 0;
            
        default:
            return DefWindowProc(hwnd, msg, wParam, lParam);
    }
}

void ClientGUI::showContextMenu(POINT pt) {
    if (!hTrayWnd_) return; 

    HMENU menu = CreatePopupMenu();
    if (!menu) return;
    
    AppendMenuW(menu, MF_STRING, ID_SHOW_STATUS, 
               statusWindowVisible.load() ? L"Hide Status Window" : L"Show Status Window");
    AppendMenuW(menu, MF_STRING, ID_SHOW_CONSOLE, L"Toggle Console");
    AppendMenuW(menu, MF_SEPARATOR, 0, nullptr);
    AppendMenuW(menu, MF_STRING, ID_EXIT, L"Exit");
    
    SetForegroundWindow(hTrayWnd_); 
    
    TrackPopupMenu(menu, TPM_RIGHTBUTTON | TPM_BOTTOMALIGN | TPM_LEFTALIGN, 
                   pt.x, pt.y, 0, 
                   hTrayWnd_, 
                   nullptr);
    
    DestroyMenu(menu); 
}

void ClientGUI::updateStatusWindow() {
    if (!statusWindow) return; // Only check if window exists, not if visible
    
    GUIStatus status;
    {
        EnterCriticalSection(&statusLock);
        status = currentStatus; 
        LeaveCriticalSection(&statusLock);
    }
      // Use a modern dark theme background with gradient effect
    RECT rect;
    GetClientRect(statusWindow, &rect);
    HDC hdc = GetDC(statusWindow);
    if (!hdc) return;    // Create gradient background - PROFESSIONAL DEEP BLUE THEME
    TRIVERTEX vertex[2];
    vertex[0].x = 0;
    vertex[0].y = 0;
    vertex[0].Red = 0x0800;   // Deep navy blue
    vertex[0].Green = 0x1500; // Rich blue
    vertex[0].Blue = 0x3000;  // Professional blue
    vertex[0].Alpha = 0x0000;
    
    vertex[1].x = rect.right;
    vertex[1].y = rect.bottom;
    vertex[1].Red = 0x1200;   // Lighter blue
    vertex[1].Green = 0x2200; // Accent blue
    vertex[1].Blue = 0x4500;  // Beautiful blue
    vertex[1].Alpha = 0x0000;
    
    GRADIENT_RECT gRect;
    gRect.UpperLeft = 0;
    gRect.LowerRight = 1;    // Professional gradient fill with elegant fallback
    if (!GradientFill(hdc, vertex, 2, &gRect, 1, GRADIENT_FILL_RECT_V)) {
        HBRUSH bgBrush = CreateSolidBrush(RGB(15, 30, 60)); // Professional dark blue
        FillRect(hdc, &rect, bgBrush);
        DeleteObject(bgBrush);
    }
    
    SetBkMode(hdc, TRANSPARENT);
    int y = 20;
    int lineHeight = 28; 
    HFONT hFont = CreateFontW(16, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                           DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                           CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY,
                           DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    HFONT hTitleFont = nullptr;
    HFONT hOldFont = (HFONT)SelectObject(hdc, hFont ? hFont : (HFONT)GetStockObject(DEFAULT_GUI_FONT));    // Title with modern white text for dark theme
    SetTextColor(hdc, RGB(255, 255, 255)); 
    hTitleFont = CreateFontW(28, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
                                DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                                CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY,
                                DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    SelectObject(hdc, hTitleFont ? hTitleFont : hFont);
    
    std::wstring titleText = L"�🔥 ULTRA MODERN BACKUP CLIENT - UPDATED! 🔥🚀";
    TextOutW(hdc, 20, y, titleText.c_str(), static_cast<int>(titleText.length()));
    y += 45;
    
    // Draw separator line with modern accent color
    HPEN hPen = CreatePen(PS_SOLID, 2, RGB(0, 173, 255));
    HPEN hOldPen = (HPEN)SelectObject(hdc, hPen);
    MoveToEx(hdc, 20, y, nullptr);
    LineTo(hdc, rect.right - 20, y);
    SelectObject(hdc, hOldPen);
    DeleteObject(hPen);
    y += 25;
    
    // Switch back to normal font
    SelectObject(hdc, hFont ? hFont : (HFONT)GetStockObject(DEFAULT_GUI_FONT));    // Connection status with dynamic messaging and beautiful colors
    std::wstring connText;
    COLORREF statusColor;
    
    if (status.connected) {
        connText = L"🟢 CONNECTED - Secure backup ready";
        statusColor = RGB(46, 204, 113); // Beautiful green
    } else {
        // Show more specific status based on phase
        if (status.phase == "Initializing" || status.phase == "Connection Setup") {
            connText = L"🔄 CONNECTING - Establishing secure link...";
            statusColor = RGB(52, 152, 219); // Professional blue
        } else if (status.phase == "Authentication") {
            connText = L"🔐 AUTHENTICATING - Verifying credentials...";
            statusColor = RGB(155, 89, 182); // Purple
        } else if (status.phase == "File Transfer") {
            connText = L"📤 TRANSFERRING - Uploading files...";
            statusColor = RGB(26, 188, 156); // Teal
        } else {
            connText = L"🔴 DISCONNECTED - Click to retry connection";
            statusColor = RGB(231, 76, 60); // Elegant red
        }
    }
    SetTextColor(hdc, statusColor); 
    HFONT hStatusFont = CreateFontW(20, 0, 0, 0, FW_SEMIBOLD, FALSE, FALSE, FALSE,
                                    DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                                    CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY,
                                    DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    SelectObject(hdc, hStatusFont);
    TextOutW(hdc, 20, y, connText.c_str(), static_cast<int>(connText.length()));
    SelectObject(hdc, hFont);
    DeleteObject(hStatusFont);
    y += 35;
    
    SetTextColor(hdc, RGB(220, 220, 220)); // Light text for dark theme
    
    // Phase with icon
    std::wstring phaseText = L"📋 Phase: " + std::wstring(status.phase.begin(), status.phase.end());
    TextOutW(hdc, 20, y, phaseText.c_str(), static_cast<int>(phaseText.length()));
    y += lineHeight;
    
    if (!status.operation.empty()) {
        std::wstring opText = L"⚙️ Operation: " + std::wstring(status.operation.begin(), status.operation.end());
        TextOutW(hdc, 20, y, opText.c_str(), static_cast<int>(opText.length()));
        y += lineHeight;
    }
    
    if (status.totalProgress > 0) {
        long long percentage = (status.totalProgress > 0) ? ((long long)status.progress * 100) / status.totalProgress : 0;
        std::wstring progText = L"📊 Progress: " + std::to_wstring(status.progress) + 
                               L"/" + std::to_wstring(status.totalProgress) + 
                               L" (" + std::to_wstring(percentage) + L"%)";
        TextOutW(hdc, 20, y, progText.c_str(), static_cast<int>(progText.length()));
        y += lineHeight;    // Modern progress bar with beautiful glass effect
    RECT progRect = {20, y, rect.right - 20, y + 30};
    
    // Outer frame with subtle shadow
    HBRUSH shadowBrush = CreateSolidBrush(RGB(5, 10, 20));
    RECT shadowRect = progRect;
    OffsetRect(&shadowRect, 2, 2);
    FillRect(hdc, &shadowRect, shadowBrush);
    DeleteObject(shadowBrush);
    
    // Main progress bar background
    HBRUSH darkBrush = CreateSolidBrush(RGB(25, 35, 50));
    FillRect(hdc, &progRect, darkBrush);
    DeleteObject(darkBrush);
    
    if (status.progress > 0 && status.totalProgress > 0) {
        RECT fillRect = progRect;
        fillRect.left += 4; fillRect.top += 4; fillRect.right -= 4; fillRect.bottom -= 4;
        fillRect.right = fillRect.left + ((fillRect.right - fillRect.left) * status.progress) / status.totalProgress;
        
        // Beautiful cyan-blue gradient progress fill
        TRIVERTEX progVertex[2];
        progVertex[0].x = fillRect.left;
        progVertex[0].y = fillRect.top;
        progVertex[0].Red = 0x0000;   // Cyan start
        progVertex[0].Green = 0xBF00; // Bright cyan
        progVertex[0].Blue = 0xFF00;  // Pure cyan
        progVertex[0].Alpha = 0x0000;
        
        progVertex[1].x = fillRect.right;
        progVertex[1].y = fillRect.bottom;
        progVertex[1].Red = 0x0000;   // Blue end
        progVertex[1].Green = 0x7F00; // Medium blue
        progVertex[1].Blue = 0xFF00;  // Pure blue
        progVertex[1].Alpha = 0x0000;
        
        GRADIENT_RECT progGradient;
        progGradient.UpperLeft = 0;
        progGradient.LowerRight = 1;
        
        if (!GradientFill(hdc, progVertex, 2, &progGradient, 1, GRADIENT_FILL_RECT_H)) {
            // Fallback to solid cyan
            HBRUSH progressBrush = CreateSolidBrush(RGB(0, 191, 255));
            FillRect(hdc, &fillRect, progressBrush);
            DeleteObject(progressBrush);
        }
        
        // Add glass highlight effect
        RECT highlightRect = fillRect;
        highlightRect.bottom = highlightRect.top + (highlightRect.bottom - highlightRect.top) / 3;
        HBRUSH highlightBrush = CreateSolidBrush(RGB(255, 255, 255));
        BLENDFUNCTION blend = {AC_SRC_OVER, 0, 50, 0};
        // Simulate alpha blending with white overlay
        FillRect(hdc, &highlightRect, highlightBrush);        DeleteObject(highlightBrush);
    }
    y += 40;
    }
    
    if (!status.speed.empty()) {
        std::wstring speedText = L"🚀 Speed: " + std::wstring(status.speed.begin(), status.speed.end());
        TextOutW(hdc, 20, y, speedText.c_str(), static_cast<int>(speedText.length()));
        y += lineHeight;
    }
    
    if (!status.eta.empty()) {
        std::wstring etaText = L"⏱️ ETA: " + std::wstring(status.eta.begin(), status.eta.end());
        TextOutW(hdc, 20, y, etaText.c_str(), static_cast<int>(etaText.length()));
        y += lineHeight;
    }
      if (!status.error.empty()) {
        SetTextColor(hdc, RGB(255, 99, 99)); // Bright red for errors in dark theme
        std::wstring errorText = L"❌ Error: " + std::wstring(status.error.begin(), status.error.end());
        TextOutW(hdc, 20, y, errorText.c_str(), static_cast<int>(errorText.length()));
        y += lineHeight;
    }
    
    // Add some padding before buttons
    y += 20;
    
    // Clean up fonts
    SelectObject(hdc, hOldFont);
    if (hFont) DeleteObject(hFont);
    if (hTitleFont) DeleteObject(hTitleFont);
    ReleaseDC(statusWindow, hdc);
}

void ClientGUI::updatePhase(const std::string& phase) {
    EnterCriticalSection(&statusLock);
    currentStatus.phase = phase;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    if (guiInitialized.load() && hTrayWnd_) {
        std::wstring tooltip = L"Backup Client - " + std::wstring(phase.begin(), phase.end());
        wcsncpy_s(trayIcon.szTip, ARRAYSIZE(trayIcon.szTip), tooltip.c_str(), _TRUNCATE);
        Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
    }
}

void ClientGUI::updateOperation(const std::string& operation, bool success, const std::string& details) {
    EnterCriticalSection(&statusLock);
    currentStatus.operation = operation;
    currentStatus.success = success;
    currentStatus.details = details;
    if (!success && !details.empty()) {
        currentStatus.error = details; 
    } else if (success) {
        currentStatus.error.clear(); 
    }
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateProgress(int current, int total, const std::string& speed, const std::string& eta) {
    EnterCriticalSection(&statusLock);
    currentStatus.progress = current;
    currentStatus.totalProgress = total;
    currentStatus.speed = speed;
    currentStatus.eta = eta;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::updateConnectionStatus(bool connected) {
    EnterCriticalSection(&statusLock);
    currentStatus.connected = connected;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
    
    if (guiInitialized.load() && hTrayWnd_) { 
         Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
    }
}

void ClientGUI::updateError(const std::string& error) {
    EnterCriticalSection(&statusLock);
    currentStatus.error = error;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

void ClientGUI::showNotification(const std::string& title, const std::string& message, DWORD iconType) {
    if (!guiInitialized.load() || !hTrayWnd_) return; 
    
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    wcsncpy_s(trayIcon.szInfoTitle, ARRAYSIZE(trayIcon.szInfoTitle), wTitle.c_str(), _TRUNCATE);
    wcsncpy_s(trayIcon.szInfo, ARRAYSIZE(trayIcon.szInfo), wMessage.c_str(), _TRUNCATE);
    trayIcon.dwInfoFlags = iconType; 
    
    trayIcon.uFlags |= NIF_INFO; 

    Shell_NotifyIconW(NIM_MODIFY, &trayIcon);
}

void ClientGUI::showPopup(const std::string& title, const std::string& message, UINT type) {
    std::wstring wTitle(title.begin(), title.end());
    std::wstring wMessage(message.begin(), message.end());
    
    MessageBoxW(statusWindowVisible.load() ? statusWindow : nullptr, wMessage.c_str(), wTitle.c_str(), type);
}

void ClientGUI::toggleStatusWindow() {
    showStatusWindow(!statusWindowVisible.load());
}

void ClientGUI::showStatusWindow(bool show) {
    if (!statusWindow) return;
    
    statusWindowVisible.store(show);
    ShowWindow(statusWindow, show ? SW_SHOW : SW_HIDE);
    
    if (show) {
        SetForegroundWindow(statusWindow); 
        SetWindowPos(statusWindow, HWND_TOPMOST, 0, 0, 0, 0, SWP_NOMOVE | SWP_NOSIZE); 
        InvalidateRect(statusWindow, nullptr, TRUE); 
        OutputDebugStringW(L"Status window set to show with TOPMOST.\n");
    } else {
        OutputDebugStringW(L"Status window set to hide.\n");
    }
}

void ClientGUI::toggleConsoleWindow() {
    if (consoleWindow) { 
        bool visible = IsWindowVisible(consoleWindow) != FALSE;
        ShowWindow(consoleWindow, visible ? SW_HIDE : SW_SHOW);
    }
}

void ClientGUI::showConsoleWindow(bool show) {
    if (consoleWindow) {
        ShowWindow(consoleWindow, show ? SW_SHOW : SW_HIDE);
    }
}

void ClientGUI::shutdown() {
    if (!guiInitialized.load() && !guiThread.joinable()) { 
         return;
    }
    
    shouldClose.store(true); 
    
    // Try to post a WM_QUIT message to the GUI thread's message queue.
    // GetThreadId requires Windows XP SP1 or later.
    // guiThread.native_handle() gives the underlying thread handle.
    DWORD guiThreadId = GetThreadId(guiThread.native_handle());
    if (guiThreadId != 0) { // Check if GetThreadId was successful
       PostThreadMessage(guiThreadId, WM_QUIT, 0, 0);
    } else if (hTrayWnd_) { 
        // Fallback if GetThreadId failed, try posting to one of its windows.
        // This isn't as direct but can wake up GetMessage.
         PostMessage(hTrayWnd_, WM_NULL, 0, 0); // Wake GetMessage
    }


    if (guiThread.joinable()) {
        guiThread.join();
    }
}

void ClientGUI::cleanup() {
    if (hTrayWnd_ && trayIcon.hWnd) { 
        trayIcon.uFlags = 0; 
        Shell_NotifyIconW(NIM_DELETE, &trayIcon);
        trayIcon.hWnd = nullptr; 
    }
    
    if (statusWindow) {
        DestroyWindow(statusWindow);
        statusWindow = nullptr;
    }
    // UnregisterClassW calls are optional as OS cleans up, but good practice for DLLs
    // HINSTANCE hInstance = GetModuleHandle(nullptr);
    // UnregisterClassW(STATUS_WINDOW_CLASS, hInstance);
    // UnregisterClassW(TRAY_WINDOW_CLASS, hInstance);
}

void ClientGUI::setRetryCallback(std::function<void()> callback) {
    retryCallback = callback;
}

void ClientGUI::updateServerInfo(const std::string& ip, int port, const std::string& filename) {
    // Update the current status with server information
    EnterCriticalSection(&statusLock);
    currentStatus.operation = "Server: " + ip + ":" + std::to_string(port);
    currentStatus.details = "File: " + filename;
    LeaveCriticalSection(&statusLock);
    
    if (statusWindow) {
        PostMessage(statusWindow, WM_STATUS_UPDATE, 0, 0);
    }
}

// Test function to simulate a working backup progress (for demonstration)
void ClientGUI::simulateWorkingBackup() {
    if (!guiInitialized.load()) return;
    
    // Simulate connection sequence
    updatePhase("Connection Setup");
    updateOperation("Connecting to server", true, "*************:1256");
    std::this_thread::sleep_for(std::chrono::milliseconds(1000));
    
    updateConnectionStatus(true);
    updateOperation("Connected successfully", true, "Secure connection established");
    std::this_thread::sleep_for(std::chrono::milliseconds(800));
    
    // Simulate authentication
    updatePhase("Authentication");
    updateOperation("Authenticating", true, "Verifying credentials...");
    std::this_thread::sleep_for(std::chrono::milliseconds(1200));
    
    updateOperation("Authenticated", true, "Access granted");
    std::this_thread::sleep_for(std::chrono::milliseconds(500));
    
    // Simulate file transfer with progress
    updatePhase("File Transfer");
    for (int i = 0; i <= 100; i += 5) {
        updateProgress(i, 100, "Uploading files...", "");
        updateOperation("Uploading", true, "File " + std::to_string(i/10 + 1) + " of 20");
        
        // Update speed and ETA
        std::string speed = std::to_string(50 + (i % 30)) + " KB/s";
        std::string eta = std::to_string(10 - (i/10)) + " seconds remaining";
        updateProgress(i, 100, speed, eta);
        
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
    
    updatePhase("Backup Complete");
    updateOperation("Backup completed successfully", true, "All files uploaded securely");
    showNotification("Backup Complete", "Your files have been backed up successfully!", NIIF_INFO);
}

#endif // _WIN32
