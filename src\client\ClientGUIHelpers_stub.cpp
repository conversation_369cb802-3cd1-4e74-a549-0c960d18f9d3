// Stub implementation for ClientGUIHelpers to resolve linker errors

#include <string>
#include <iostream>
#include "../../include/client/ClientGUI.h"

void ClientGUIHelpers::showNotification(const std::string& title, const std::string& message, unsigned long duration) {
    // STUB: Simply output to console instead of showing a GUI notification
    std::cout << "[NOTIFICATION] " << title << ": " << message << " (Duration: " << duration << "ms)" << std::endl;
}
